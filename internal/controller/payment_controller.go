package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
	"github.com/smooth-inc/backend/pkg/errors"
)

type PaymentController struct {
	paymentUsecase       usecase.PaymentUsecase
	paymentMethodUsecase usecase.PaymentMethodUsecase
	logger               *logger.Logger
}

func NewPaymentController(paymentUsecase usecase.PaymentUsecase, paymentMethodUsecase usecase.PaymentMethodUsecase, logger *logger.Logger) *PaymentController {
	return &PaymentController{
		paymentUsecase:       paymentUsecase,
		paymentMethodUsecase: paymentMethodUsecase,
		logger:               logger,
	}
}

func (pc *PaymentController) GetPayments(c *gin.Context, params api.GetPaymentsParams) {
	pc.logger.LogInfo(c.Request.Context(), "Fetching user payments", map[string]interface{}{
		"limit":  params.Limit,
		"offset": params.Offset,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment access attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	limit := 20
	offset := 0
	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	payments, err := pc.paymentUsecase.GetByUserID(c.Request.Context(), userID, limit, offset)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to fetch user payments", map[string]interface{}{
			"user_id": userID,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to fetch payments")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully fetched user payments", map[string]interface{}{
		"user_id":       userID,
		"payment_count": len(payments),
	})

	response.Success(c, payments)
}

func (pc *PaymentController) PostPaymentsWebhooksStripe(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Processing Stripe webhook")
	response.InternalServerError(c, "NOT_IMPLEMENTED", "Stripe webhook not implemented yet")
}

func (pc *PaymentController) PostPaymentsSessionIdCreatePaymentLink(c *gin.Context, sessionId types.UUID) {
	pc.logger.LogInfo(c.Request.Context(), "Creating payment link", map[string]interface{}{
		"session_id": sessionId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment link creation attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	sessionUUID := uuid.UUID(sessionId)
	paymentLink, err := pc.paymentUsecase.CreatePaymentLink(c.Request.Context(), sessionUUID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to create payment link", map[string]interface{}{
			"user_id":    userID,
			"session_id": sessionId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to create payment link")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully created payment link", map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionId,
	})

	response.Success(c, map[string]interface{}{
		"payment_link": paymentLink,
	})
}

func (pc *PaymentController) PostPaymentsSessionIdProcessAutoPayment(c *gin.Context, sessionId types.UUID) {
	pc.logger.LogInfo(c.Request.Context(), "Processing auto payment", map[string]interface{}{
		"session_id": sessionId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized auto payment attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	sessionUUID := uuid.UUID(sessionId)
	payment, err := pc.paymentUsecase.ProcessAutoPayment(c.Request.Context(), sessionUUID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to process auto payment", map[string]interface{}{
			"user_id":    userID,
			"session_id": sessionId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to process auto payment")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully processed auto payment", map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionId,
		"payment_id": payment.ID,
	})

	response.Success(c, payment)
}

func (pc *PaymentController) GetPaymentMethods(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Fetching user payment methods")

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment methods access attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethods, err := pc.paymentMethodUsecase.GetByUserID(c.Request.Context(), userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to fetch payment methods", map[string]interface{}{
			"user_id": userID,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to fetch payment methods")
		}
		return
	}

	var apiPaymentMethods []api.PaymentMethod
	for _, pm := range paymentMethods {
		idStr := pm.ID.String()
		typeStr := string(pm.Type)
		apiPM := api.PaymentMethod{
			Id:        &idStr,
			Type:      (*api.PaymentMethodType)(&typeStr),
			IsDefault: &pm.IsDefault,
			CreatedAt: &pm.CreatedAt,
			UpdatedAt: &pm.UpdatedAt,
		}

		if pm.Brand != nil {
			apiPM.Brand = pm.Brand
		}
		if pm.Last4 != nil {
			apiPM.Last4 = pm.Last4
		}
		if pm.ExpiryMonth != nil {
			apiPM.ExpiryMonth = pm.ExpiryMonth
		}
		if pm.ExpiryYear != nil {
			apiPM.ExpiryYear = pm.ExpiryYear
		}
		if pm.StripePaymentMethodID != "" {
			apiPM.StripePaymentMethodId = &pm.StripePaymentMethodID
		}

		apiPaymentMethods = append(apiPaymentMethods, apiPM)
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully fetched payment methods", map[string]interface{}{
		"user_id":              userID,
		"payment_method_count": len(apiPaymentMethods),
	})

	response.Success(c, apiPaymentMethods)
}

func (pc *PaymentController) PostPaymentsMethods(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Creating setup intent for payment method")

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized setup intent creation attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	setupIntent, err := pc.paymentMethodUsecase.CreateSetupIntent(c.Request.Context(), userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to create setup intent", map[string]interface{}{
			"user_id": userID,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to create setup intent")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully created setup intent", map[string]interface{}{
		"user_id":         userID,
		"setup_intent_id": setupIntent.ID,
	})

	response.Success(c, map[string]interface{}{
		"id":            setupIntent.ID,
		"client_secret": setupIntent.ClientSecret,
		"status":        setupIntent.Status,
	})
}

func (pc *PaymentController) DeletePaymentsMethodsPaymentMethodId(c *gin.Context, paymentMethodId types.UUID) {
	pc.logger.LogInfo(c.Request.Context(), "Deleting payment method", map[string]interface{}{
		"payment_method_id": paymentMethodId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment method deletion attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethodUUID := uuid.UUID(paymentMethodId)

	err = pc.paymentMethodUsecase.Delete(c.Request.Context(), paymentMethodUUID, userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to delete payment method", map[string]interface{}{
			"user_id":           userID,
			"payment_method_id": paymentMethodId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to delete payment method")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully deleted payment method", map[string]interface{}{
		"user_id":           userID,
		"payment_method_id": paymentMethodId,
	})

	response.Success(c, map[string]interface{}{
		"message": "Payment method deleted successfully",
	})
}

func (pc *PaymentController) PostPaymentsMethodsPaymentMethodIdSetDefault(c *gin.Context, paymentMethodId types.UUID) {
	pc.logger.LogInfo(c.Request.Context(), "Setting default payment method", map[string]interface{}{
		"payment_method_id": paymentMethodId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized default payment method setting attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethodUUID := uuid.UUID(paymentMethodId)

	err = pc.paymentMethodUsecase.SetAsDefault(c.Request.Context(), paymentMethodUUID, userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to set default payment method", map[string]interface{}{
			"user_id":           userID,
			"payment_method_id": paymentMethodId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to set default payment method")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully set default payment method", map[string]interface{}{
		"user_id":           userID,
		"payment_method_id": paymentMethodId,
	})

	response.Success(c, map[string]interface{}{
		"message": "Payment method set as default successfully",
	})
}

func (pc *PaymentController) PostPaymentsMethodsStripeCallback(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Processing Stripe callback")

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized Stripe callback attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	var req api.StripeSetupCallbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Invalid Stripe callback request", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	isDefault := false
	if req.IsDefault != nil {
		isDefault = *req.IsDefault
	}

	paymentMethod, err := pc.paymentMethodUsecase.AttachPaymentMethod(
		c.Request.Context(),
		userID,
		req.PaymentMethodId,
		req.SetupIntentId,
		isDefault,
	)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to attach payment method", map[string]interface{}{
			"user_id":           userID,
			"payment_method_id": req.PaymentMethodId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to attach payment method")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully attached payment method", map[string]interface{}{
		"user_id":           userID,
		"payment_method_id": paymentMethod.ID,
	})

	response.Success(c, map[string]interface{}{
		"id":         paymentMethod.ID.String(),
		"type":       string(paymentMethod.Type),
		"is_default": paymentMethod.IsDefault,
		"brand":      paymentMethod.Brand,
		"last4":      paymentMethod.Last4,
	})
}

func (pc *PaymentController) GetPaymentsMethodsValidateSetup(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Validating setup intent")

	setupIntentID := c.Query("setup_intent_id")
	if setupIntentID == "" {
		pc.logger.LogWarn(c.Request.Context(), "Missing setup_intent_id parameter")
		response.BadRequest(c, "MISSING_PARAMETER", "setup_intent_id parameter is required", nil)
		return
	}

	setupIntent, err := pc.paymentMethodUsecase.ValidateSetupIntent(c.Request.Context(), setupIntentID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to validate setup intent", map[string]interface{}{
			"setup_intent_id": setupIntentID,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to validate setup intent")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully validated setup intent", map[string]interface{}{
		"setup_intent_id": setupIntentID,
		"status":          setupIntent.Status,
	})

	response.Success(c, map[string]interface{}{
		"id":     setupIntent.ID,
		"status": setupIntent.Status,
		"valid":  setupIntent.Status == "succeeded",
	})
}
