package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
	"github.com/smooth-inc/backend/pkg/errors"
)

type PaymentController struct {
	paymentUsecase       usecase.PaymentUsecase
	paymentMethodUsecase usecase.PaymentMethodUsecase
	logger               *logger.Logger
}

func NewPaymentController(paymentUsecase usecase.PaymentUsecase, paymentMethodUsecase usecase.PaymentMethodUsecase, logger *logger.Logger) *PaymentController {
	return &PaymentController{
		paymentUsecase:       paymentUsecase,
		paymentMethodUsecase: paymentMethodUsecase,
		logger:               logger,
	}
}

func (pc *PaymentController) GetPayments(c *gin.Context, params api.GetPaymentsParams) {
	pc.logger.LogInfo(c.Request.Context(), "Fetching user payments", map[string]interface{}{
		"limit":  params.Limit,
		"offset": params.Offset,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment access attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	limit := 20
	offset := 0
	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	payments, err := pc.paymentUsecase.GetByUserID(c.Request.Context(), userID, limit, offset)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to fetch user payments", map[string]interface{}{
			"user_id": userID,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to fetch payments")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully fetched user payments", map[string]interface{}{
		"user_id":       userID,
		"payment_count": len(payments),
	})

	var apiPayments []api.Payment
	for _, payment := range payments {
		apiPayment := api.Payment{
			Id:                    &payment.ID,
			SessionId:             &payment.SessionID,
			UserId:                &payment.UserID,
			Amount:                &payment.Amount,
			Currency:              &payment.Currency,
			Status:                (*api.PaymentStatus)(&payment.Status),
			CreatedAt:             &payment.CreatedAt,
			UpdatedAt:             &payment.UpdatedAt,
		}

		if payment.StripePaymentIntentID != nil {
			apiPayment.StripePaymentIntentId = payment.StripePaymentIntentID
		}
		if payment.StripePaymentLinkID != nil {
			apiPayment.StripePaymentLinkId = payment.StripePaymentLinkID
		}
		if payment.StripeStatus != nil {
			apiPayment.StripeStatus = payment.StripeStatus
		}
		if payment.PaymentMethodType != nil {
			apiPayment.PaymentMethodType = payment.PaymentMethodType
		}
		if payment.CardLast4 != nil {
			apiPayment.CardLast4 = payment.CardLast4
		}
		if payment.CardBrand != nil {
			apiPayment.CardBrand = payment.CardBrand
		}
		if payment.PaidAt != nil {
			apiPayment.PaidAt = payment.PaidAt
		}
		if payment.ReceiptURL != nil {
			apiPayment.ReceiptUrl = payment.ReceiptURL
		}
		if payment.InvoiceNumber != nil {
			apiPayment.InvoiceNumber = payment.InvoiceNumber
		}
		if payment.FailureReason != nil {
			apiPayment.FailureReason = payment.FailureReason
		}
		apiPayment.RetryCount = &payment.RetryCount

		apiPayments = append(apiPayments, apiPayment)
	}

	response.Success(c, apiPayments)
}

func (pc *PaymentController) PostPaymentsWebhooksStripe(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Processing Stripe webhook")

	payload, err := c.GetRawData()
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to read webhook payload")
		response.BadRequest(c, "INVALID_PAYLOAD", "Failed to read webhook payload", err.Error())
		return
	}

	signature := c.GetHeader("Stripe-Signature")
	if signature == "" {
		pc.logger.LogWarn(c.Request.Context(), "Missing Stripe signature header", map[string]interface{}{
			"headers": c.Request.Header,
		})
		response.BadRequest(c, "MISSING_SIGNATURE", "Missing Stripe signature header", nil)
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Processing webhook with signature", map[string]interface{}{
		"signature_length": len(signature),
		"payload_length":   len(payload),
	})

	err = pc.paymentUsecase.ProcessWebhook(c.Request.Context(), payload, signature)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to process Stripe webhook")
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "WEBHOOK_ERROR", "Failed to process webhook")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully processed Stripe webhook")
	response.Success(c, map[string]interface{}{
		"message": "Webhook processed successfully",
	})
}

func (pc *PaymentController) PostPaymentsSessionIdCreatePaymentLink(c *gin.Context, sessionId types.UUID) {
	pc.logger.LogInfo(c.Request.Context(), "Creating payment link", map[string]interface{}{
		"session_id": sessionId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment link creation attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	sessionUUID := uuid.UUID(sessionId)
	paymentLink, err := pc.paymentUsecase.CreatePaymentLink(c.Request.Context(), sessionUUID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to create payment link", map[string]interface{}{
			"user_id":    userID,
			"session_id": sessionId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to create payment link")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully created payment link", map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionId,
	})

	response.Success(c, map[string]interface{}{
		"url": paymentLink,
	})
}

func (pc *PaymentController) PostPaymentsSessionIdProcessAutoPayment(c *gin.Context, sessionId types.UUID) {
	pc.logger.LogInfo(c.Request.Context(), "Processing auto payment", map[string]interface{}{
		"session_id": sessionId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized auto payment attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	sessionUUID := uuid.UUID(sessionId)
	payment, err := pc.paymentUsecase.ProcessAutoPayment(c.Request.Context(), sessionUUID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to process auto payment", map[string]interface{}{
			"user_id":    userID,
			"session_id": sessionId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to process auto payment")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully processed auto payment", map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionId,
		"payment_id": payment.ID,
	})

	apiPayment := api.Payment{
		Id:        &payment.ID,
		SessionId: &payment.SessionID,
		UserId:    &payment.UserID,
		Amount:    &payment.Amount,
		Currency:  &payment.Currency,
		Status:    (*api.PaymentStatus)(&payment.Status),
		CreatedAt: &payment.CreatedAt,
		UpdatedAt: &payment.UpdatedAt,
	}

	if payment.StripePaymentIntentID != nil {
		apiPayment.StripePaymentIntentId = payment.StripePaymentIntentID
	}
	if payment.PaymentMethodType != nil {
		apiPayment.PaymentMethodType = payment.PaymentMethodType
	}
	if payment.CardLast4 != nil {
		apiPayment.CardLast4 = payment.CardLast4
	}
	if payment.CardBrand != nil {
		apiPayment.CardBrand = payment.CardBrand
	}
	if payment.PaidAt != nil {
		apiPayment.PaidAt = payment.PaidAt
	}
	if payment.ReceiptURL != nil {
		apiPayment.ReceiptUrl = payment.ReceiptURL
	}
	apiPayment.RetryCount = &payment.RetryCount

	response.Success(c, apiPayment)
}

func (pc *PaymentController) GetPaymentMethods(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Fetching user payment methods")

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment methods access attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethods, err := pc.paymentMethodUsecase.GetByUserID(c.Request.Context(), userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to fetch payment methods", map[string]interface{}{
			"user_id": userID,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to fetch payment methods")
		}
		return
	}

	var apiPaymentMethods []api.PaymentMethod
	for _, pm := range paymentMethods {
		idStr := pm.ID.String()
		typeStr := string(pm.Type)
		apiPM := api.PaymentMethod{
			Id:        &idStr,
			Type:      (*api.PaymentMethodType)(&typeStr),
			IsDefault: &pm.IsDefault,
			CreatedAt: &pm.CreatedAt,
			UpdatedAt: &pm.UpdatedAt,
		}

		if pm.Brand != nil {
			apiPM.Brand = pm.Brand
		}
		if pm.Last4 != nil {
			apiPM.Last4 = pm.Last4
		}
		if pm.ExpiryMonth != nil {
			apiPM.ExpiryMonth = pm.ExpiryMonth
		}
		if pm.ExpiryYear != nil {
			apiPM.ExpiryYear = pm.ExpiryYear
		}
		if pm.StripePaymentMethodID != "" {
			apiPM.StripePaymentMethodId = &pm.StripePaymentMethodID
		}

		apiPaymentMethods = append(apiPaymentMethods, apiPM)
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully fetched payment methods", map[string]interface{}{
		"user_id":              userID,
		"payment_method_count": len(apiPaymentMethods),
	})

	response.Success(c, apiPaymentMethods)
}

func (pc *PaymentController) PostPaymentMethods(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Creating setup intent for payment method")

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized setup intent creation attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	setupIntent, err := pc.paymentMethodUsecase.CreateSetupIntent(c.Request.Context(), userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to create setup intent", map[string]interface{}{
			"user_id": userID,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to create setup intent")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully created setup intent", map[string]interface{}{
		"user_id":         userID,
		"setup_intent_id": setupIntent.ID,
	})

	response.Success(c, map[string]interface{}{
		"id":            setupIntent.ID,
		"client_secret": setupIntent.ClientSecret,
		"status":        setupIntent.Status,
	})
}

func (pc *PaymentController) DeletePaymentMethodsPaymentMethodId(c *gin.Context, paymentMethodId string) {
	pc.logger.LogInfo(c.Request.Context(), "Deleting payment method", map[string]interface{}{
		"payment_method_id": paymentMethodId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment method deletion attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethodUUID, err := uuid.Parse(paymentMethodId)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Invalid payment method ID format", map[string]interface{}{
			"payment_method_id": paymentMethodId,
		})
		response.BadRequest(c, "INVALID_ID", "Invalid payment method ID format", err.Error())
		return
	}

	err = pc.paymentMethodUsecase.Delete(c.Request.Context(), paymentMethodUUID, userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to delete payment method", map[string]interface{}{
			"user_id":           userID,
			"payment_method_id": paymentMethodId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to delete payment method")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully deleted payment method", map[string]interface{}{
		"user_id":           userID,
		"payment_method_id": paymentMethodId,
	})

	response.Success(c, map[string]interface{}{
		"message": "Payment method deleted successfully",
	})
}

func (pc *PaymentController) PostPaymentMethodsPaymentMethodIdSetDefault(c *gin.Context, paymentMethodId string) {
	pc.logger.LogInfo(c.Request.Context(), "Setting default payment method", map[string]interface{}{
		"payment_method_id": paymentMethodId,
	})

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized default payment method setting attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethodUUID, err := uuid.Parse(paymentMethodId)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Invalid payment method ID format", map[string]interface{}{
			"payment_method_id": paymentMethodId,
		})
		response.BadRequest(c, "INVALID_ID", "Invalid payment method ID format", err.Error())
		return
	}

	err = pc.paymentMethodUsecase.SetAsDefault(c.Request.Context(), paymentMethodUUID, userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to set default payment method", map[string]interface{}{
			"user_id":           userID,
			"payment_method_id": paymentMethodId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to set default payment method")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully set default payment method", map[string]interface{}{
		"user_id":           userID,
		"payment_method_id": paymentMethodId,
	})

	response.Success(c, map[string]interface{}{
		"message": "Payment method set as default successfully",
	})
}

func (pc *PaymentController) PostPaymentMethodsStripeCallback(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Processing Stripe callback")

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized Stripe callback attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	var req api.StripeSetupCallbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Invalid Stripe callback request", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	isDefault := false
	if req.IsDefault != nil {
		isDefault = *req.IsDefault
	}

	paymentMethod, err := pc.paymentMethodUsecase.AttachPaymentMethod(
		c.Request.Context(),
		userID,
		req.PaymentMethodId,
		req.SetupIntentId,
		isDefault,
	)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to attach payment method", map[string]interface{}{
			"user_id":           userID,
			"payment_method_id": req.PaymentMethodId,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to attach payment method")
		}
		return
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully attached payment method", map[string]interface{}{
		"user_id":           userID,
		"payment_method_id": paymentMethod.ID,
	})

	idStr := paymentMethod.ID.String()
	typeStr := string(paymentMethod.Type)
	apiPaymentMethod := api.PaymentMethod{
		Id:        &idStr,
		Type:      (*api.PaymentMethodType)(&typeStr),
		IsDefault: &paymentMethod.IsDefault,
		Brand:     paymentMethod.Brand,
		Last4:     paymentMethod.Last4,
		CreatedAt: &paymentMethod.CreatedAt,
		UpdatedAt: &paymentMethod.UpdatedAt,
	}

	if paymentMethod.StripePaymentMethodID != "" {
		apiPaymentMethod.StripePaymentMethodId = &paymentMethod.StripePaymentMethodID
	}
	if paymentMethod.ExpiryMonth != nil {
		apiPaymentMethod.ExpiryMonth = paymentMethod.ExpiryMonth
	}
	if paymentMethod.ExpiryYear != nil {
		apiPaymentMethod.ExpiryYear = paymentMethod.ExpiryYear
	}

	response.Success(c, apiPaymentMethod)
}

func (pc *PaymentController) GetPaymentMethodsValidateSetup(c *gin.Context) {
	pc.logger.LogInfo(c.Request.Context(), "Validating payment method setup")

	userID, err := GetUserIDFromContext(c)
	if err != nil {
		pc.logger.LogWarn(c.Request.Context(), "Unauthorized payment method validation attempt")
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethods, err := pc.paymentMethodUsecase.GetByUserID(c.Request.Context(), userID)
	if err != nil {
		pc.logger.LogError(c.Request.Context(), err, "Failed to fetch payment methods for validation", map[string]interface{}{
			"user_id": userID,
		})
		if appErr, ok := errors.IsAppError(err); ok {
			response.InternalServerError(c, string(appErr.Code), appErr.Message)
		} else {
			response.InternalServerError(c, "INTERNAL_ERROR", "Failed to validate payment setup")
		}
		return
	}

	hasValidPaymentMethod := false
	hasDefaultPaymentMethod := false

	for _, pm := range paymentMethods {
		if pm.CanBeUsedForPayment() {
			hasValidPaymentMethod = true
			if pm.IsDefault {
				hasDefaultPaymentMethod = true
				break
			}
		}
	}

	pc.logger.LogInfo(c.Request.Context(), "Successfully validated payment setup", map[string]interface{}{
		"user_id":                   userID,
		"has_valid_payment_method":  hasValidPaymentMethod,
		"has_default_payment_method": hasDefaultPaymentMethod,
	})

	response.Success(c, map[string]interface{}{
		"has_payment_method": hasValidPaymentMethod,
		"has_default":        hasDefaultPaymentMethod,
		"ready_for_auto_pay": hasDefaultPaymentMethod,
	})
}
