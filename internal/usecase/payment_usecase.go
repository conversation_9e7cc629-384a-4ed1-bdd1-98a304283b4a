package usecase

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/gateway/stripe"
	"github.com/smooth-inc/backend/internal/repository"
	"github.com/smooth-inc/backend/pkg/errors"
)

type paymentUsecase struct {
	paymentRepo       repository.PaymentRepository
	sessionRepo       repository.SessionRepository
	userRepo          repository.UserRepository
	paymentMethodRepo repository.PaymentMethodRepository
	stripeGateway     stripe.Gateway
}

func NewPaymentUsecase(
	paymentRepo repository.PaymentRepository,
	sessionRepo repository.SessionRepository,
	userRepo repository.UserRepository,
	paymentMethodRepo repository.PaymentMethodRepository,
	stripeGateway stripe.Gateway,
) PaymentUsecase {
	return &paymentUsecase{
		paymentRepo:       paymentRepo,
		sessionRepo:       sessionRepo,
		userRepo:          userRepo,
		paymentMethodRepo: paymentMethodRepo,
		stripeGateway:     stripeGateway,
	}
}

func (uc *paymentUsecase) CreatePaymentLink(ctx context.Context, sessionID uuid.UUID) (string, error) {
	session, err := uc.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return "", errors.NewNotFoundError("session not found")
	}

	if session.Status != domain.SessionStatusCompleted {
		return "", errors.NewBadRequestError("session must be completed to create payment link")
	}

	existingPayment, err := uc.paymentRepo.GetBySessionID(ctx, sessionID)
	if err == nil && existingPayment != nil {
		if existingPayment.StripePaymentLinkID != nil {
			return *existingPayment.StripePaymentLinkID, nil
		}
	}

	if session.UserID == nil {
		return "", errors.NewBadRequestError("session has no associated user")
	}

	user, err := uc.userRepo.GetByID(ctx, *session.UserID)
	if err != nil {
		return "", errors.NewNotFoundError("user not found")
	}

	amount := session.GetFinalAmount()
	if amount <= 0 {
		return "", errors.NewBadRequestError("invalid session fee amount")
	}

	payment, err := domain.NewPayment(sessionID, *session.UserID, amount)
	if err != nil {
		return "", errors.NewValidationError(err.Error())
	}

	paymentLink, err := uc.stripeGateway.CreatePaymentLink(ctx, amount, "JPY", fmt.Sprintf("Parking fee for session %s", sessionID.String()))
	if err != nil {
		return "", errors.NewExternalServiceError("failed to create Stripe payment link", err)
	}

	payment.SetStripePaymentLinkID(paymentLink.ID)
	payment.SetStripePaymentIntentID(paymentLink.PaymentIntentID)

	if existingPayment != nil {
		payment.ID = existingPayment.ID
		if err := uc.paymentRepo.Update(ctx, payment); err != nil {
			return "", errors.NewDatabaseError("failed to update payment", err)
		}
	} else {
		if err := uc.paymentRepo.Create(ctx, payment); err != nil {
			return "", errors.NewDatabaseError("failed to create payment", err)
		}
	}

	return paymentLink.URL, nil
}

func (uc *paymentUsecase) ProcessWebhook(ctx context.Context, payload []byte, signature string) error {
	event, err := uc.stripeGateway.VerifyWebhook(ctx, payload, signature)
	if err != nil {
		return errors.NewBadRequestError("invalid webhook signature")
	}

	switch event.Type {
	case "payment_intent.succeeded":
		return uc.handlePaymentIntentSucceeded(ctx, event.Data)
	case "payment_intent.payment_failed":
		return uc.handlePaymentIntentFailed(ctx, event.Data)
	case "setup_intent.succeeded":
		return uc.handleSetupIntentSucceeded(ctx, event.Data)
	default:
		return nil
	}
}

func (uc *paymentUsecase) handlePaymentIntentSucceeded(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return errors.NewNotFoundError("payment not found")
	}

	payment.MarkAsCompleted()
	
	if receiptURL, ok := data["receipt_url"].(string); ok {
		payment.SetReceiptURL(receiptURL)
	}

	return uc.paymentRepo.Update(ctx, payment)
}

func (uc *paymentUsecase) handlePaymentIntentFailed(ctx context.Context, data map[string]interface{}) error {
	paymentIntentID, ok := data["id"].(string)
	if !ok {
		return errors.NewBadRequestError("invalid payment intent ID")
	}

	payment, err := uc.paymentRepo.GetByStripePaymentIntentID(ctx, paymentIntentID)
	if err != nil {
		return errors.NewNotFoundError("payment not found")
	}

	failureReason := "Payment failed"
	if lastPaymentError, ok := data["last_payment_error"].(map[string]interface{}); ok {
		if message, ok := lastPaymentError["message"].(string); ok {
			failureReason = message
		}
	}

	payment.MarkAsFailed(failureReason)
	return uc.paymentRepo.Update(ctx, payment)
}

func (uc *paymentUsecase) handleSetupIntentSucceeded(ctx context.Context, data map[string]interface{}) error {
	return nil
}

func (uc *paymentUsecase) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error) {
	return uc.paymentRepo.GetByUserID(ctx, userID, limit, offset)
}

func (uc *paymentUsecase) ProcessAutoPayment(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error) {
	session, err := uc.sessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return nil, errors.NewNotFoundError("session not found")
	}

	if session.Status != domain.SessionStatusCompleted {
		return nil, errors.NewBadRequestError("session must be completed to process auto payment")
	}

	if session.UserID == nil {
		return nil, errors.NewBadRequestError("session has no associated user")
	}

	user, err := uc.userRepo.GetByID(ctx, *session.UserID)
	if err != nil {
		return nil, errors.NewNotFoundError("user not found")
	}

	if !user.AutoPaymentEnabled {
		return nil, errors.NewBadRequestError("auto payment is not enabled for this user")
	}

	if user.DefaultPaymentMethodID == nil {
		return nil, errors.NewBadRequestError("user has no default payment method")
	}

	defaultPaymentMethodID, err := uuid.Parse(*user.DefaultPaymentMethodID)
	if err != nil {
		return nil, errors.NewBadRequestError("invalid default payment method ID")
	}

	paymentMethod, err := uc.paymentMethodRepo.GetByID(ctx, defaultPaymentMethodID)
	if err != nil {
		return nil, errors.NewNotFoundError("default payment method not found")
	}

	if !paymentMethod.CanBeUsedForPayment() {
		return nil, errors.NewBadRequestError("default payment method cannot be used for payment")
	}

	amount := session.GetFinalAmount()
	if amount <= 0 {
		return nil, errors.NewBadRequestError("invalid session fee amount")
	}

	existingPayment, err := uc.paymentRepo.GetBySessionID(ctx, sessionID)
	if err == nil && existingPayment != nil && existingPayment.IsCompleted() {
		return existingPayment, nil
	}

	payment, err := domain.NewPayment(sessionID, *session.UserID, amount)
	if err != nil {
		return nil, errors.NewValidationError(err.Error())
	}

	if user.StripeCustomerID == nil {
		return nil, errors.NewBadRequestError("user has no Stripe customer ID")
	}

	paymentIntent, err := uc.stripeGateway.CreatePaymentIntent(ctx, amount, "JPY", *user.StripeCustomerID, paymentMethod.StripePaymentMethodID)
	if err != nil {
		return nil, errors.NewExternalServiceError("failed to create payment intent", err)
	}

	payment.SetStripePaymentIntentID(paymentIntent.ID)
	payment.MarkAsProcessing()

	if paymentMethod.Brand != nil && paymentMethod.Last4 != nil {
		payment.SetPaymentMethod(string(paymentMethod.Type), *paymentMethod.Last4, *paymentMethod.Brand)
	}

	if existingPayment != nil {
		payment.ID = existingPayment.ID
		if err := uc.paymentRepo.Update(ctx, payment); err != nil {
			return nil, errors.NewDatabaseError("failed to update payment", err)
		}
	} else {
		if err := uc.paymentRepo.Create(ctx, payment); err != nil {
			return nil, errors.NewDatabaseError("failed to create payment", err)
		}
	}

	confirmResult, err := uc.stripeGateway.ConfirmPaymentIntent(ctx, paymentIntent.ID)
	if err != nil {
		payment.MarkAsFailed("Failed to confirm payment")
		uc.paymentRepo.Update(ctx, payment)
		return nil, errors.NewExternalServiceError("failed to confirm payment intent", err)
	}

	if confirmResult.Status == "succeeded" {
		payment.MarkAsCompleted()
		if confirmResult.ReceiptURL != "" {
			payment.SetReceiptURL(confirmResult.ReceiptURL)
		}
	} else if confirmResult.Status == "requires_action" {
		return nil, errors.NewBadRequestError("payment requires additional authentication")
	} else {
		payment.MarkAsFailed("Payment confirmation failed")
	}

	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		return nil, errors.NewDatabaseError("failed to update payment status", err)
	}

	return payment, nil
}
